'use client';

import { useState, useEffect } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	LoadingSpinner,
	Switch,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { Role } from '@prisma/client';
import { X, UserCog } from 'lucide-react';

interface User {
	id: string;
	username?: string;
	email?: string;
	name?: string;
	role: Role;
	disabled: boolean;
	provider: string;
}

interface EditUserFormProps {
	user: User;
	onSuccess: () => void;
	onCancel: () => void;
}

export function EditUserForm({ user, onSuccess, onCancel }: EditUserFormProps) {
	const [formData, setFormData] = useState({
		email: user.email || '',
		name: user.name || '',
		role: user.role,
		disabled: user.disabled,
		newPassword: '',
		confirmPassword: '',
	});
	const [loading, setLoading] = useState(false);
	const { showSuccess, showError } = useToast();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Validate password if provided
		if (formData.newPassword) {
			if (formData.newPassword !== formData.confirmPassword) {
				showError(new Error('Passwords do not match'));
				return;
			}
			if (formData.newPassword.length < 6) {
				showError(new Error('Password must be at least 6 characters long'));
				return;
			}
		}

		setLoading(true);

		try {
			const updateData: any = {
				email: formData.email.trim() || undefined,
				name: formData.name.trim() || undefined,
				role: formData.role,
				disabled: formData.disabled,
			};

			// Only include password if it's provided
			if (formData.newPassword) {
				updateData.password = formData.newPassword;
			}

			const response = await fetch(`/api/admin/users/${user.id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(updateData),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess('User updated successfully');
				onSuccess();
			} else {
				throw new Error(data.error || 'Failed to update user');
			}
		} catch (error) {
			showError(new Error('Failed to update user'));
		} finally {
			setLoading(false);
		}
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center">
						<UserCog className="h-5 w-5 mr-2" />
						Edit User
					</CardTitle>
					<Button variant="ghost" size="sm" onClick={onCancel}>
						<X className="h-4 w-4" />
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* User Info */}
					<div className="space-y-2">
						<Label>Username</Label>
						<Input
							value={user.username || 'N/A'}
							disabled
							className="bg-gray-50 dark:bg-gray-800"
						/>
						<p className="text-xs text-muted-foreground">
							Username cannot be changed
						</p>
					</div>

					<div className="space-y-2">
						<Label>Provider</Label>
						<Input
							value={user.provider}
							disabled
							className="bg-gray-50 dark:bg-gray-800"
						/>
						<p className="text-xs text-muted-foreground">
							Provider cannot be changed
						</p>
					</div>

					{/* Email */}
					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							type="email"
							value={formData.email}
							onChange={(e) => setFormData({ ...formData, email: e.target.value })}
							placeholder="Enter email"
						/>
					</div>

					{/* Name */}
					<div className="space-y-2">
						<Label htmlFor="name">Name</Label>
						<Input
							id="name"
							type="text"
							value={formData.name}
							onChange={(e) => setFormData({ ...formData, name: e.target.value })}
							placeholder="Enter full name"
						/>
					</div>

					{/* Role */}
					<div className="space-y-2">
						<Label htmlFor="role">Role</Label>
						<Select
							value={formData.role}
							onValueChange={(value: Role) => setFormData({ ...formData, role: value })}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={Role.USER}>User</SelectItem>
								<SelectItem value={Role.ADMIN}>Admin</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* Account Status */}
					<div className="flex items-center justify-between">
						<div className="space-y-0.5">
							<Label htmlFor="disabled">Account Status</Label>
							<p className="text-xs text-muted-foreground">
								{formData.disabled ? 'Account is disabled' : 'Account is active'}
							</p>
						</div>
						<Switch
							id="disabled"
							checked={!formData.disabled}
							onCheckedChange={(checked) =>
								setFormData({ ...formData, disabled: !checked })
							}
						/>
					</div>

					{/* Password Change (only for USERNAME_PASSWORD provider) */}
					{user.provider === 'USERNAME_PASSWORD' && (
						<>
							<div className="border-t pt-4">
								<Label className="text-sm font-medium">Change Password (Optional)</Label>
							</div>

							<div className="space-y-2">
								<Label htmlFor="newPassword">New Password</Label>
								<Input
									id="newPassword"
									type="password"
									value={formData.newPassword}
									onChange={(e) =>
										setFormData({ ...formData, newPassword: e.target.value })
									}
									placeholder="Enter new password"
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="confirmPassword">Confirm New Password</Label>
								<Input
									id="confirmPassword"
									type="password"
									value={formData.confirmPassword}
									onChange={(e) =>
										setFormData({ ...formData, confirmPassword: e.target.value })
									}
									placeholder="Confirm new password"
								/>
							</div>
						</>
					)}

					{/* Submit Buttons */}
					<div className="flex space-x-2 pt-4">
						<Button type="submit" disabled={loading} className="flex-1">
							{loading ? (
								<>
									<LoadingSpinner className="h-4 w-4 mr-2" />
									Updating...
								</>
							) : (
								'Update User'
							)}
						</Button>
						<Button type="button" variant="outline" onClick={onCancel}>
							Cancel
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
