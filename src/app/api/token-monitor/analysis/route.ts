import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { UnauthorizedError } from '@/backend/errors';
import { verifyToken } from '@/backend/utils/token.util';
import { tokenMonitor } from '@/backend/services/token-monitor.service';

/**
 * GET /api/token-monitor/analysis
 * Get comprehensive cost analysis
 */
async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// Get cost analysis from token monitor
		const analysis = tokenMonitor.getCostAnalysis();

		return NextResponse.json({
			success: true,
			data: analysis,
		});
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: 401 }
			);
		}

		console.error('Error getting cost analysis:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

export { withErrorHandling(GET) as GET };
