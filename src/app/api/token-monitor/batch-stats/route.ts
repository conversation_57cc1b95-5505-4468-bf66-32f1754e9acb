import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { UnauthorizedError } from '@/backend/errors';
import { verifyToken } from '@/backend/utils/token.util';

/**
 * GET /api/token-monitor/batch-stats
 * Get batch processing statistics
 */
async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// TODO: Implement actual batch processing statistics
		// For now, return mock data to prevent API errors
		const batchStats = {
			totalBatches: 0,
			completedBatches: 0,
			failedBatches: 0,
			averageBatchSize: 0,
			averageProcessingTime: 0,
			totalItemsProcessed: 0,
			successRate: 0,
			currentlyProcessing: 0,
		};

		return NextResponse.json({
			success: true,
			data: batchStats,
		});
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ success: false, error: error.message },
				{ status: 401 }
			);
		}

		console.error('Error getting batch stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

export { withErrorHandling(GET) as GET };
