import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService, getUserService } from '@/backend/wire';
import { Role, Provider } from '@prisma/client';
import { z } from 'zod';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

// Validation schemas
const updateUserSchema = z.object({
	email: z.string().email().optional(),
	name: z.string().min(1).max(100).optional(),
	role: z.nativeEnum(Role).optional(),
	disabled: z.boolean().optional(),
	password: z.string().min(6).optional(),
});

/**
 * GET /api/admin/users/[id]
 * Get specific user details by ID
 */
async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id } = await params;

		if (!id) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		const adminService = getAdminService();
		const user = await adminService.getUserById(id);

		if (!user) {
			return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
		}

		// Remove sensitive information
		const { password_hash, ...safeUser } = user;

		return NextResponse.json({
			success: true,
			data: safeUser,
		});
	} catch (error) {
		console.error('Error fetching user:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * PUT /api/admin/users/[id]
 * Update user information
 */
async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: userId } = await params;
		if (!userId) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		const body = await request.json();
		const { email, name, role, disabled, password } = updateUserSchema.parse(body);

		const adminService = getAdminService();
		const userService = getUserService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);

		// Get current user to check if it exists
		const currentUser = await adminService.getUserById(userId);
		if (!currentUser) {
			return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
		}

		// Handle role change
		if (role !== undefined && role !== currentUser.role) {
			await adminService.updateUserRole(
				userId,
				role,
				undefined, // adminId will be extracted from middleware
				ipAddress,
				userAgent
			);
		}

		// Handle account status change
		if (disabled !== undefined && disabled !== currentUser.disabled) {
			if (disabled) {
				await adminService.disableUser(
					userId,
					undefined, // adminId will be extracted from middleware
					ipAddress,
					userAgent
				);
			} else {
				await adminService.enableUser(
					userId,
					undefined, // adminId will be extracted from middleware
					ipAddress,
					userAgent
				);
			}
		}

		// Handle password change (only for USERNAME_PASSWORD provider)
		if (password && currentUser.provider === Provider.USERNAME_PASSWORD) {
			const bcrypt = await import('bcryptjs');
			const passwordHash = await bcrypt.hash(password, 12);

			await adminService.changeUserPassword(
				userId,
				passwordHash,
				undefined, // adminId will be extracted from middleware
				ipAddress,
				userAgent
			);
		}

		// Update other fields (email, name) if any
		const updateData: any = {};
		if (email !== undefined) updateData.email = email;
		if (name !== undefined) updateData.name = name;

		if (Object.keys(updateData).length > 0) {
			await adminService.updateUserProfile(userId, updateData);
		}

		// Get updated user
		const updatedUser = await adminService.getUserById(userId);
		const { password_hash, ...safeUser } = updatedUser;

		return NextResponse.json({
			success: true,
			data: safeUser,
		});
	} catch (error) {
		console.error('Error updating user:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE /api/admin/users/[id]
 * Delete user and all related data
 */
async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: userId } = await params;
		if (!userId) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);

		// Get current user to check if it exists
		const currentUser = await adminService.getUserById(userId);
		if (!currentUser) {
			return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
		}

		// Prevent deleting the last admin
		if (currentUser.role === Role.ADMIN) {
			const adminCount = await adminService.getAdminCount();
			if (adminCount <= 1) {
				return NextResponse.json(
					{ success: false, error: 'Cannot delete the last admin user' },
					{ status: 400 }
				);
			}
		}

		// Delete user and all related data
		await adminService.deleteUser(
			userId,
			undefined, // adminId will be extracted from middleware
			ipAddress,
			userAgent
		);

		return NextResponse.json({
			success: true,
			message: 'User deleted successfully',
		});
	} catch (error) {
		console.error('Error deleting user:', error);

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPUT = withAdminAuth(withErrorHandling(PUT));
const wrappedDELETE = withAdminAuth(withErrorHandling(DELETE));

export { wrappedGET as GET, wrappedPUT as PUT, wrappedDELETE as DELETE };
